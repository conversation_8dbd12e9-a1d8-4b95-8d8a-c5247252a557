<view class="container data-v-57280228" style="{{'background:'+(backgroundGradient)+';'}}"><view class="main-content data-v-57280228"><block wx:if="{{merchantInfo}}"><view class="merchant-info data-v-57280228"><block wx:if="{{logoImageUrl}}"><view class="logo-container data-v-57280228"><image class="logo-img data-v-57280228" src="{{logoImageUrl}}" mode="aspectFit"></image></view></block><view class="merchant-name data-v-57280228">{{merchantInfo.merchantName}}</view><block wx:if="{{merchantInfo.address}}"><view class="merchant-address data-v-57280228">{{merchantInfo.address}}</view></block><block wx:if="{{tableInfo}}"><view class="table-info data-v-57280228"><view class="table-number data-v-57280228">{{"桌台："+(tableInfo.tableName||tableInfo.tableNumber)}}</view></view></block></view></block></view><view class="fixed-bottom-buttons data-v-57280228"><view data-event-opts="{{[['tap',[['goToOrder',['$event']]]]]}}" class="{{['function-btn','order-btn','data-v-57280228',(!orderButtonEnabled)?'disabled':'']}}" bindtap="__e"><view class="btn-icon data-v-57280228">🍽️</view><view class="btn-text data-v-57280228">{{''+(orderButtonEnabled?'点餐':'点餐('+countdown+'s)')+''}}</view></view><view data-event-opts="{{[['tap',[['goToLottery',['$event']]]]]}}" class="function-btn lottery-btn data-v-57280228" bindtap="__e"><view class="btn-icon data-v-57280228">🎁</view><view class="btn-text data-v-57280228">抽奖</view></view></view><block wx:if="{{showWelcomeOverlay}}"><view data-event-opts="{{[['tap',[['closeWelcomeOverlay',['$event']]]]]}}" class="welcome-overlay data-v-57280228" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="overlay-content data-v-57280228" catchtap="__e"><block wx:if="{{backgroundImageUrl}}"><view class="overlay-image-container data-v-57280228"><block wx:if="{{welcomeText}}"><label class="welcome-text _span data-v-57280228">{{welcomeText}}</label></block><image class="overlay-image data-v-57280228" src="{{backgroundImageUrl}}" mode="aspectFit"></image><label class="countdown-text _span data-v-57280228">{{''+overlayCountdown+"秒后自动关闭"}}</label></view></block></view></view></block><u-modal vue-id="8dd740cc-1" title="系统提示" show-cancel-button="{{false}}" confirm-text="我知道了" value="{{expiredModal.show}}" data-event-opts="{{[['^confirm',[['e0']]],['^input',[['__set_model',['$0','show','$event',[]],['expiredModal']]]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-57280228" bind:__l="__l" vue-slots="{{['default']}}"><view class="expired-content data-v-57280228"><view class="expired-icon data-v-57280228">⚠️</view><view class="expired-text data-v-57280228">{{expiredModal.message}}</view></view></u-modal></view>