{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/index/index.vue?2386", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/index/index.vue?1175", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/index/index.vue?5de9", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/index/index.vue?4659", "uni-app:///pages/index/index.vue", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/index/index.vue?5d8a", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/index/index.vue?8070"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "merchantCode", "tableNumber", "merchantInfo", "tableInfo", "merchantConfig", "currentActivity", "expiredModal", "show", "message", "orderButtonEnabled", "countdown", "countdownTimer", "showWelcomeOverlay", "overlayCountdown", "overlayTimer", "computed", "uiConfig", "console", "backgroundImageStyle", "backgroundImageUrl", "logoImageUrl", "welcomeText", "pageTitle", "primaryColor", "backgroundGradient", "onLoad", "onUnload", "clearInterval", "watch", "handler", "uni", "title", "immediate", "frontColor", "backgroundColor", "methods", "initPage", "setTimeout", "loadMerchantInfo", "merchantApi", "res", "loadTableInfo", "tableApi", "loadMerchantConfig", "config<PERSON>pi", "loadCurrentActivity", "lotteryApi", "startOrderButtonCountdown", "goToOrder", "icon", "url", "goToLottery", "handleError", "duration", "showWelcomeOverlayWithTimer", "closeWelcomeOverlay", "adjustColor"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgEv1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;UACA;QACA;UACAC;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;IACA;IAEA;IACA;EACA;EAEAC;IACA;IACA;MACAC;IACA;IACA;MACAA;IACA;EACA;EAEAC;IACA;IACAN;MACAO;QACA;UACAC;YACAC;UACA;QACA;MACA;MACAC;IACA;IAEA;IACAT;MACAM;QACA;UACAC;YACAG;YACAC;UACA;QACA;MACA;MACAF;IACA;EACA;EAEAG;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAEA;gBACA;kBACAC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGApB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAF;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAJ;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA4B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAN;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8B;MAAA;MACA;MACA;MAEA;QACA;QACA;UACA;UACApB;UACA;QACA;MACA;IACA;IAEAqB;MACA;MACA;QACAlB;UACAC;UACAkB;QACA;QACA;MACA;MAEA;QACAnB;UACAC;UACAkB;QACA;QACA;MACA;;MAEA;MACAnB;QACAoB;MACA;IACA;IAEAC;MACA;MACArB;QACAoB;MACA;IACA;IAEAE;MACA;;MAEA;MACA;QACA;QACA;MACA;QACAtB;UACAC;UACAkB;UACAI;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;QAEA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA5B;QACA;MACA;IACA;IAEA;IACA6B;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzXA;AAAA;AAAA;AAAA;AAA0jD,CAAgB,07CAAG,EAAC,C;;;;;;;;;;;ACA9kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-modal/u-modal\" */ \"@/uni_modules/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.expiredModal.show = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\" :style=\"{ background: backgroundGradient }\">\n    <!-- 主要内容区域 -->\n    <view class=\"main-content\">\n\n\n\n      <!-- 商家信息 -->\n      <view class=\"merchant-info\" v-if=\"merchantInfo\">\n        <!-- Logo图片 -->\n        <view class=\"logo-container\" v-if=\"logoImageUrl\">\n          <image :src=\"logoImageUrl\" class=\"logo-img\" mode=\"aspectFit\"></image>\n        </view>\n        <view class=\"merchant-name\">{{ merchantInfo.merchantName }}</view>\n        <view class=\"merchant-address\" v-if=\"merchantInfo.address\">{{ merchantInfo.address }}</view>\n        <!-- 桌台信息 -->\n        <view class=\"table-info\" v-if=\"tableInfo\">\n          <view class=\"table-number\">桌台：{{ tableInfo.tableName || tableInfo.tableNumber }}</view>\n        </view>\n      </view>\n\n\n    </view>\n\n    <!-- 固定在底部的功能按钮 -->\n    <view class=\"fixed-bottom-buttons\">\n      <view class=\"function-btn order-btn\" :class=\"{ 'disabled': !orderButtonEnabled }\" @click=\"goToOrder\">\n        <view class=\"btn-icon\">🍽️</view>\n        <view class=\"btn-text\">\n          {{ orderButtonEnabled ? '点餐' : `点餐(${countdown}s)` }}\n        </view>\n      </view>\n      <view class=\"function-btn lottery-btn\" @click=\"goToLottery\">\n        <view class=\"btn-icon\">🎁</view>\n        <view class=\"btn-text\">抽奖</view>\n      </view>\n    </view>\n\n    <!-- 欢迎遮罩层 -->\n    <view class=\"welcome-overlay\" v-if=\"showWelcomeOverlay\" @click=\"closeWelcomeOverlay\">\n      <view class=\"overlay-content\" @click.stop>\n\n        <!-- 图片 -->\n        <view class=\"overlay-image-container\" v-if=\"backgroundImageUrl\">\n          <span v-if=\"welcomeText\" class=\"welcome-text\">{{ welcomeText }}</span>\n          <image :src=\"backgroundImageUrl\" class=\"overlay-image\" mode=\"aspectFit\"></image>\n          <span class=\"countdown-text\"> {{ overlayCountdown }}秒后自动关闭</span>\n        </view>\n\n      </view>\n    </view>\n\n    <!-- 到期提示弹窗 -->\n    <u-modal v-model=\"expiredModal.show\" title=\"系统提示\" :show-cancel-button=\"false\" confirm-text=\"我知道了\"\n      @confirm=\"expiredModal.show = false\">\n      <view class=\"expired-content\">\n        <view class=\"expired-icon\">⚠️</view>\n        <view class=\"expired-text\">{{ expiredModal.message }}</view>\n      </view>\n    </u-modal>\n  </view>\n</template>\n\n<script>\nimport { merchantApi, tableApi, configApi, lotteryApi, getImageUrl } from '@/utils/api.js'\n\nexport default {\n  data() {\n    return {\n      merchantCode: '',\n      tableNumber: '',\n      merchantInfo: null,\n      tableInfo: null,\n      merchantConfig: {},\n      currentActivity: null,\n      expiredModal: {\n        show: false,\n        message: ''\n      },\n      // 点餐按钮倒计时相关\n      orderButtonEnabled: false,\n      countdown: 5,\n      countdownTimer: null,\n      // 欢迎遮罩层相关\n      showWelcomeOverlay: false,\n      overlayCountdown: 10,\n      overlayTimer: null\n    }\n  },\n\n  computed: {\n    // 解析UI配置\n    uiConfig() {\n      const uiConfigStr = this.merchantConfig.ui_config\n      if (uiConfigStr) {\n        try {\n          return JSON.parse(uiConfigStr)\n        } catch (e) {\n          console.error('UI配置解析失败:', e)\n          return {}\n        }\n      }\n      return {}\n    },\n\n    // 背景图片样式\n    backgroundImageStyle() {\n      const backgroundImage = this.uiConfig.backgroundImage || this.merchantConfig.scan_page_bg\n      return !!backgroundImage\n    },\n\n    // 背景图片URL\n    backgroundImageUrl() {\n      const backgroundImage = this.uiConfig.backgroundImage || this.merchantConfig.scan_page_bg\n      if (backgroundImage) {\n        return getImageUrl(backgroundImage)\n      }\n      return ''\n    },\n\n    // Logo图片URL\n    logoImageUrl() {\n      const logoImage = this.uiConfig.logoImage\n      return logoImage ? getImageUrl(logoImage) : ''\n    },\n\n    // 欢迎语\n    welcomeText() {\n      return this.uiConfig.welcomeText || ''\n    },\n\n    // 页面标题\n    pageTitle() {\n      return this.uiConfig.pageTitle || '抽奖点餐'\n    },\n\n    // 主题色彩\n    primaryColor() {\n      return this.uiConfig.primaryColor || '#667eea'\n    },\n\n    // 背景渐变色\n    backgroundGradient() {\n      const color = this.primaryColor\n      // 生成基于主题色的渐变背景\n      return `linear-gradient(135deg, ${color} 0%, ${this.adjustColor(color, -20)} 100%)`\n    }\n  },\n\n  onLoad(options) {\n    // 从URL参数或扫码获取商家编码和桌台号\n    this.merchantCode = options.merchantCode || '002'\n    this.tableNumber = options.tableNumber || 'A002'\n\n    this.initPage()\n    this.startOrderButtonCountdown()\n  },\n\n  onUnload() {\n    // 清除倒计时器\n    if (this.countdownTimer) {\n      clearInterval(this.countdownTimer)\n    }\n    if (this.overlayTimer) {\n      clearInterval(this.overlayTimer)\n    }\n  },\n\n  watch: {\n    // 监听页面标题变化，动态设置导航栏标题\n    pageTitle: {\n      handler(newTitle) {\n        if (newTitle) {\n          uni.setNavigationBarTitle({\n            title: newTitle\n          })\n        }\n      },\n      immediate: true\n    },\n\n    // 监听主题色彩变化，动态设置导航栏颜色\n    primaryColor: {\n      handler(newColor) {\n        if (newColor) {\n          uni.setNavigationBarColor({\n            frontColor: '#ffffff',\n            backgroundColor: newColor\n          })\n        }\n      },\n      immediate: true\n    }\n  },\n\n  methods: {\n    async initPage() {\n      try {\n        // 加载商家信息\n        await this.loadMerchantInfo()\n\n        // 加载桌台信息\n        if (this.tableNumber) {\n          await this.loadTableInfo()\n        }\n\n        // 加载商家配置\n        await this.loadMerchantConfig()\n\n        // 加载当前活动信息\n        await this.loadCurrentActivity()\n\n        // 页面数据加载完成后，延迟显示欢迎遮罩层\n        this.$nextTick(() => {\n          setTimeout(() => {\n            this.showWelcomeOverlayWithTimer()\n          }, 500) // 延迟500ms显示，确保页面渲染完成\n        })\n\n      } catch (error) {\n        console.error('页面初始化失败:', error)\n        this.handleError(error)\n      }\n    },\n\n    async loadMerchantInfo() {\n      try {\n        const res = await merchantApi.getMerchantInfo(this.merchantCode)\n        if (res.code === 200) {\n          this.merchantInfo = res.data\n        } else {\n          throw new Error(res.msg || '获取商家信息失败')\n        }\n      } catch (error) {\n        this.handleError(error)\n      }\n    },\n\n    async loadTableInfo() {\n      try {\n        const res = await tableApi.getTableInfo(this.merchantCode, this.tableNumber)\n        if (res.code === 200) {\n          this.tableInfo = res.data\n        } else {\n          throw new Error(res.msg || '获取桌台信息失败')\n        }\n      } catch (error) {\n        this.handleError(error)\n      }\n    },\n\n    async loadMerchantConfig() {\n      try {\n        const res = await configApi.getAllConfig(this.merchantCode)\n        if (res.code === 200) {\n          this.merchantConfig = res.data || {}\n        }\n      } catch (error) {\n        console.error('获取商家配置失败:', error)\n      }\n    },\n\n    async loadCurrentActivity() {\n      try {\n        const res = await lotteryApi.getCurrentActivity(this.merchantCode)\n        if (res.code === 200 && res.data) {\n          this.currentActivity = res.data\n        }\n      } catch (error) {\n        console.error('获取活动信息失败:', error)\n      }\n    },\n\n    // 开始点餐按钮倒计时\n    startOrderButtonCountdown() {\n      this.orderButtonEnabled = false\n      this.countdown = 5\n\n      this.countdownTimer = setInterval(() => {\n        this.countdown--\n        if (this.countdown <= 0) {\n          this.orderButtonEnabled = true\n          clearInterval(this.countdownTimer)\n          this.countdownTimer = null\n        }\n      }, 1000)\n    },\n\n    goToOrder() {\n      // 检查按钮是否可用\n      if (!this.orderButtonEnabled) {\n        uni.showToast({\n          title: `请等待 ${this.countdown} 秒后再点击`,\n          icon: 'none'\n        })\n        return\n      }\n\n      if (!this.tableInfo || !this.tableInfo.meituanLink) {\n        uni.showToast({\n          title: '点餐功能暂未开放',\n          icon: 'none'\n        })\n        return\n      }\n\n      // 跳转到美团点餐链接\n      uni.navigateTo({\n        url: `/pages/webview/webview?url=${encodeURIComponent(this.tableInfo.meituanLink)}`\n      })\n    },\n\n    goToLottery() {\n      // 跳转到抽奖页面\n      uni.navigateTo({\n        url: `/pages/lottery/lottery?merchantCode=${this.merchantCode}&tableNumber=${this.tableNumber}`\n      })\n    },\n\n    handleError(error) {\n      let message = error.message || error.msg || '系统异常'\n\n      // 检查是否是商家到期错误\n      if (message.includes('过期') || message.includes('到期')) {\n        this.expiredModal.message = message\n        this.expiredModal.show = true\n      } else {\n        uni.showToast({\n          title: message,\n          icon: 'none',\n          duration: 3000\n        })\n      }\n    },\n\n    // 显示欢迎遮罩层并启动倒计时\n    showWelcomeOverlayWithTimer() {\n      // 只有当有欢迎语或背景图片时才显示遮罩层\n      if (this.welcomeText || this.backgroundImageUrl) {\n        this.showWelcomeOverlay = true\n        this.overlayCountdown = 10\n\n        this.overlayTimer = setInterval(() => {\n          this.overlayCountdown--\n          if (this.overlayCountdown <= 0) {\n            this.closeWelcomeOverlay()\n          }\n        }, 1000)\n      }\n    },\n\n    // 关闭欢迎遮罩层\n    closeWelcomeOverlay() {\n      this.showWelcomeOverlay = false\n      if (this.overlayTimer) {\n        clearInterval(this.overlayTimer)\n        this.overlayTimer = null\n      }\n    },\n\n    // 颜色调整工具方法\n    adjustColor(color, amount) {\n      // 将十六进制颜色转换为RGB\n      const hex = color.replace('#', '')\n      const r = parseInt(hex.substr(0, 2), 16)\n      const g = parseInt(hex.substr(2, 2), 16)\n      const b = parseInt(hex.substr(4, 2), 16)\n\n      // 调整亮度\n      const newR = Math.max(0, Math.min(255, r + amount))\n      const newG = Math.max(0, Math.min(255, g + amount))\n      const newB = Math.max(0, Math.min(255, b + amount))\n\n      // 转换回十六进制\n      return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  min-height: 100vh;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  padding-bottom: 160rpx;\n  /* 为底部固定按钮留出空间 */\n}\n\n\n\n// 主要内容区域\n.main-content {\n  flex: 1;\n  padding: 40rpx 30rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 2;\n  position: relative;\n  min-height: calc(100vh - 160rpx);\n  height: calc(100vh - 160rpx);\n}\n\n// Logo容器\n.logo-container {\n  margin-bottom: 30rpx;\n\n  .logo-img {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 60rpx;\n    background: rgba(255, 255, 255, 0.9);\n    padding: 10rpx;\n    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\n  }\n}\n\n// 商家信息样式\n.merchant-info {\n  text-align: center;\n  width: 80%;\n  margin: 30rpx 0;\n  background: rgba(255, 255, 255, 0.9);\n  backdrop-filter: blur(10rpx);\n  border-radius: 20rpx;\n  padding: 40rpx 30rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\n\n  .merchant-name {\n    font-size: 42rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 15rpx;\n  }\n\n  .merchant-address {\n    font-size: 28rpx;\n    color: #666;\n    margin-bottom: 15rpx;\n  }\n\n  .table-info {\n    .table-number {\n      font-size: 28rpx;\n      color: #888;\n      background: rgba(102, 126, 234, 0.1);\n      padding: 15rpx 30rpx;\n      border-radius: 50rpx;\n      display: inline-block;\n    }\n  }\n}\n\n\n\n// 固定在底部的功能按钮\n.fixed-bottom-buttons {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  display: flex;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10rpx);\n  padding: 20rpx 30rpx;\n  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\n  z-index: 100;\n  gap: 20rpx;\n\n  .function-btn {\n    flex: 1;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 25rpx;\n    padding: 30rpx 20rpx;\n    text-align: center;\n    box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);\n    transition: all 0.3s ease;\n\n    &:active {\n      transform: scale(0.95);\n    }\n\n    &.disabled {\n      background: linear-gradient(135deg, #ccc 0%, #999 100%);\n      box-shadow: 0 4rpx 15rpx rgba(153, 153, 153, 0.3);\n\n      .btn-text {\n        color: #666;\n      }\n    }\n\n    .btn-icon {\n      font-size: 50rpx;\n      margin-bottom: 10rpx;\n    }\n\n    .btn-text {\n      font-size: 28rpx;\n      font-weight: bold;\n      color: #fff;\n    }\n  }\n\n  .order-btn {\n    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);\n    box-shadow: 0 4rpx 15rpx rgba(255, 154, 158, 0.3);\n\n    &.disabled {\n      background: linear-gradient(135deg, #ccc 0%, #999 100%);\n      box-shadow: 0 4rpx 15rpx rgba(153, 153, 153, 0.3);\n    }\n  }\n\n  .lottery-btn {\n    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n    box-shadow: 0 4rpx 15rpx rgba(168, 237, 234, 0.3);\n  }\n}\n\n// 欢迎遮罩层\n.welcome-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 160rpx; // 不遮住底部按钮区域\n  background: rgba(0, 0, 0, 0.8); // 恢复黑色遮罩背景\n  backdrop-filter: blur(10rpx);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 50; // 降低层级，确保不遮住底部按钮\n  padding: 20rpx;\n\n  .overlay-content {\n    background: transparent; // 内容区域保持透明\n    border-radius: 30rpx;\n    width: 90%;\n    max-width: 600rpx;\n    text-align: center;\n    position: relative;\n\n    .welcome-text {\n      font-size: 32rpx;\n      color: #ffffff;\n      font-weight: bold;\n      margin-bottom: 20rpx;\n      display: block;\n      text-align: center;\n    }\n\n    .countdown-text {\n      font-size: 24rpx;\n      color: #ffffff;\n      margin-top: 20rpx;\n      display: block;\n      text-align: center;\n    }\n\n    .overlay-welcome-text {\n      font-size: 42rpx;\n      color: #333;\n      font-weight: bold;\n      margin-bottom: 30rpx;\n      line-height: 1.4;\n      background: rgba(255, 255, 255, 0.95);\n      padding: 30rpx;\n      border-radius: 20rpx;\n      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);\n      backdrop-filter: blur(10rpx);\n    }\n\n    .overlay-image-container {\n      width: 100%;\n      background: transparent;\n      text-align: center;\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n\n      .overlay-image {\n        width: 100%;\n        height: 500rpx;\n        border-radius: 15rpx;\n        display: block;\n        margin: 0 auto;\n      }\n    }\n\n    .overlay-countdown {\n      font-size: 26rpx;\n      color: #666;\n      margin-top: 20rpx;\n      background: rgba(255, 255, 255, 0.95);\n      padding: 15rpx 25rpx;\n      border-radius: 20rpx;\n      display: inline-block;\n      box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);\n      backdrop-filter: blur(10rpx);\n    }\n  }\n}\n\n\n\n.expired-content {\n  text-align: center;\n  padding: 40rpx 20rpx;\n\n  .expired-icon {\n    font-size: 80rpx;\n    margin-bottom: 30rpx;\n  }\n\n  .expired-text {\n    font-size: 32rpx;\n    color: #666;\n    line-height: 1.6;\n  }\n}\n</style>", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754115094019\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}